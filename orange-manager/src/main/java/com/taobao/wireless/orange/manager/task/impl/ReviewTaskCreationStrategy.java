package com.taobao.wireless.orange.manager.task.impl;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.TaskBizType;
import com.taobao.wireless.orange.common.constant.enums.TaskType;
import com.taobao.wireless.orange.common.event.OperationRecordCreatedEvent;
import com.taobao.wireless.orange.manager.TaskManager;
import com.taobao.wireless.orange.manager.task.TaskCreationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 审核任务创建策略
 * 当操作类型为APPLY_RELEASE时，创建审核任务
 */
@Component
@Slf4j
public class ReviewTaskCreationStrategy implements TaskCreationStrategy {

    @Autowired
    private TaskManager taskManager;

    @Override
    public boolean shouldCreateTask(OperationRecordCreatedEvent event) {
        // 当操作类型为APPLY_RELEASE时，需要创建审核任务
        return OperationType.APPLY_RELEASE.equals(event.getOperationType());
    }

    @Override
    public String createTask(OperationRecordCreatedEvent event) {
        if (!shouldCreateTask(event)) {
            return null;
        }

        // 检查是否已经存在未完成的审核任务
        if (taskManager.hasUnfinishedTask(event.getReleaseVersion(), TaskBizType.RELEASE_ORDER)) {
            log.info("Review task already exists for release version: {}", event.getReleaseVersion());
            return null;
        }

        String description = String.format("发布单审核任务 - 发布版本: %s, 命名空间: %s",
                event.getReleaseVersion(), event.getNamespaceId());

        String taskId = taskManager.createTask(
                TaskType.APPROVE,
                TaskBizType.RELEASE_ORDER,
                event.getReleaseVersion(),
                description
        );

        log.info("Created review task: taskId={}, releaseVersion={}", taskId, event.getReleaseVersion());
        return taskId;
    }

    @Override
    public String[] getSupportedOperationTypes() {
        return new String[]{OperationType.APPLY_RELEASE.getCode()};
    }
}
