package com.taobao.wireless.orange.manager.release.impl;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.manager.release.AbstractOperationTemplate;
import com.taobao.wireless.orange.manager.release.OperationContext;
import org.springframework.stereotype.Component;

/**
 * 发起验证操作策略实现
 */
@Component
public class StartVerifyStrategy extends AbstractOperationTemplate {

    @Override
    public OperationType getOperationType() {
        return OperationType.START_VERIFY;
    }

    @Override
    public void validateStatus(OperationContext context) {
        var releaseOrder = context.getReleaseOrder();

        if (!ReleaseOrderStatus.IN_GRAY.equals(releaseOrder.getStatus()) && !ReleaseOrderStatus.IN_RATIO_GRAY.equals(releaseOrder.getStatus())) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_INVALID);
        }
    }

    @Override
    public void validateParameters(OperationContext context) {
    }

    @Override
    public void executeOperation(OperationContext context) {
        // todo: 钉钉通知测试同学进行验证
        // todo: 生成代办任务
    }

    @Override
    public ReleaseOrderStatus getTargetStatus(OperationContext context) {
        return ReleaseOrderStatus.WAIT_VERIFY;
    }
}