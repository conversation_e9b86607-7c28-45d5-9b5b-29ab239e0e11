package com.taobao.wireless.orange.service.model;

import com.taobao.wireless.orange.common.constant.enums.VerifyStatus;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class VerifyReplyDTO {
    /**
     * 验证结果
     */
    @NotNull(message = "验证结果不能为空")
    private VerifyStatus verifyStatus;
    /**
     * 验证信息
     */
    @NotBlank(message = "验证信息不能为空")
    private String verifyMessage;
}
