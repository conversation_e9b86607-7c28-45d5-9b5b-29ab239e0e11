package com.taobao.wireless.orange.manager.release;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.OperationStatus;
import com.taobao.wireless.orange.common.event.OperationRecordCreatedEvent;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderOperationDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderOperationDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 发布操作模板
 */
public abstract class AbstractOperationTemplate implements OperationStrategy {

    @Autowired
    protected OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private ONamespaceDAO namespaceDAO;

    @Autowired
    protected OReleaseOrderOperationDAO releaseOrderOperationDAO;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 发布单操作标准流程
     */
    @Transactional(rollbackFor = Exception.class)
    public void execute(OperationContext context) {
        Long operationId = null;
        try {
            // 锁管理
            acquireNamespaceLock(context);

            // 数据加载
            loadContextDetail(context);

            // 前置校验
            validation(context);

            // 创建操作记录 (INIT 状态)
            operationId = createOperationRecord(context);

            // 业务逻辑执行
            executeOperation(context);

            // 更新发布单状态
            updateReleaseOrderStatus(context);

            // 更新操作记录 (SUCCESS 状态)
            updateOperationStatus(operationId, OperationStatus.SUCCESS, null);
        } catch (Throwable e) {
            // 异常处理和失败状态记录
            if (operationId != null) {
                updateOperationStatus(operationId, OperationStatus.FAILED, e.getMessage());
            }
            throw e;
        } finally {
            // 释放锁
            releaseNamespaceLock(context);
        }
    }

    /**
     * 前置校验，包含状态校验、权限校验、入参校验
     */
    private void validation(OperationContext context) {
        // 状态校验
        validateStatus(context);

        // 权限校验
        validatePermission(context);

        // 入参校验
        validateParameters(context);
    }

    /**
     * 状态校验
     */
    public abstract void validateStatus(OperationContext context);

    /**
     * 权限校验
     */
    @Override
    public void validatePermission(OperationContext context) {
        var namespace = context.getNamespace();
        String workerId = ThreadContextUtil.getWorkerId();

        if (StringUtils.isBlank(workerId) || !namespace.getOwners().contains(workerId)) {
            throw new CommonException(ExceptionEnum.NO_PERMISSION);
        }
    }

    /**
     * 入参校验
     */
    public abstract void validateParameters(OperationContext context);

    /**
     * 创建操作记录
     */
    private Long createOperationRecord(OperationContext context) {
        OReleaseOrderDO releaseOrder = context.getReleaseOrder();

        OReleaseOrderOperationDO operation = new OReleaseOrderOperationDO();
        operation.setReleaseVersion(context.getReleaseVersion());
        operation.setType(getOperationType());
        operation.setAppKey(releaseOrder.getAppKey());
        operation.setNamespaceId(releaseOrder.getNamespaceId());
        operation.setParams(context.getAdditionalData() != null ? JSON.toJSONString(context.getAdditionalData()) : null);
        operation.setStatus(OperationStatus.INIT);
        releaseOrderOperationDAO.save(operation);

        // 发布操作记录创建事件
        OperationRecordCreatedEvent event = new OperationRecordCreatedEvent(
                this,
                operation.getId(),
                operation.getReleaseVersion(),
                operation.getType(),
                operation.getAppKey(),
                operation.getNamespaceId(),
                operation.getParams()
        );
        eventPublisher.publishEvent(event);

        return operation.getId();
    }

    /**
     * 更新操作记录状态
     */
    private void updateOperationStatus(Long id, OperationStatus status, String errorMessage) {
        var operation = new OReleaseOrderOperationDO();
        operation.setId(id);
        operation.setStatus(status);
        if (StringUtils.isNotBlank(errorMessage)) {
            operation.setResult(JSON.toJSONString(Map.entry("errorMessage", errorMessage)));
        }
        releaseOrderOperationDAO.updateById(operation);
    }

    /**
     * 获取上下文详情
     */
    private void loadContextDetail(OperationContext context) {
        OReleaseOrderDO releaseOrder = releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getReleaseVersion, context.getReleaseVersion())
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.RELEASE_ORDER_NOT_EXIST));
        context.setReleaseOrder(releaseOrder);

        ONamespaceDO namespace = namespaceDAO.getByNamespaceId(releaseOrder.getNamespaceId());
        if (namespace == null) {
            throw new CommonException(ExceptionEnum.NAMESPACE_NOT_EXIST);
        }
        context.setNamespace(namespace);
    }

    /**
     * 更新发布单状态的默认实现
     */
    @Override
    public void updateReleaseOrderStatus(OperationContext context) {
        releaseOrderDAO.lambdaUpdate()
                .eq(OReleaseOrderDO::getReleaseVersion, context.getReleaseVersion())
                .set(OReleaseOrderDO::getStatus, getTargetStatus(context))
                .update();
    }

    /**
     * 获取 namespace 锁
     */
    private void acquireNamespaceLock(OperationContext context) {
        // TODO: 实现具体的锁获取逻辑
    }

    /**
     * 释放 namespace 锁
     */
    private void releaseNamespaceLock(OperationContext context) {
        // TODO: 实现具体的锁释放逻辑
    }
}