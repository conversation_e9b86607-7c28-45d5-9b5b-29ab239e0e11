package com.taobao.wireless.orange.manager;

import com.taobao.wireless.orange.common.constant.enums.TaskStatus;
import com.taobao.wireless.orange.dal.enhanced.dao.OTaskHandlerDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OTaskHandlerDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 任务处理人管理器
 */
@Service
@Slf4j
public class TaskHandlerManager {

    @Autowired
    private OTaskHandlerDAO taskHandlerDAO;

    /**
     * 为任务分配处理人
     *
     * @param taskId  任务ID
     * @param userIds 处理人ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void assignHandlers(String taskId, List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }

        for (String userId : userIds) {
            OTaskHandlerDO handler = new OTaskHandlerDO();
            handler.setTaskId(taskId);
            handler.setUserId(userId);
            handler.setStatus(TaskStatus.PENDING.getCode());
            taskHandlerDAO.save(handler);
        }

        log.info("Assigned handlers to task: taskId={}, handlers={}", taskId, userIds);
    }

    /**
     * 更新任务处理人状态
     *
     * @param taskId 任务ID
     * @param userId 处理人ID
     * @param status 新状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateHandlerStatus(String taskId, String userId, TaskStatus status) {
        taskHandlerDAO.lambdaUpdate()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .eq(OTaskHandlerDO::getUserId, userId)
                .set(OTaskHandlerDO::getStatus, status.getCode())
                .update();

        log.info("Updated task handler status: taskId={}, userId={}, status={}", taskId, userId, status.getCode());
    }

    /**
     * 获取任务的所有处理人
     *
     * @param taskId 任务ID
     * @return 处理人列表
     */
    public List<OTaskHandlerDO> getTaskHandlers(String taskId) {
        return taskHandlerDAO.lambdaQuery()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .list();
    }

    /**
     * 检查任务是否有指定的处理人
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否有该处理人
     */
    public boolean hasHandler(String taskId, String userId) {
        return taskHandlerDAO.lambdaQuery()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .eq(OTaskHandlerDO::getUserId, userId)
                .exists();
    }

    /**
     * 移除任务的所有处理人
     *
     * @param taskId 任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeAllHandlers(String taskId) {
        taskHandlerDAO.lambdaUpdate()
                .eq(OTaskHandlerDO::getTaskId, taskId)
                .remove();

        log.info("Removed all handlers from task: taskId={}", taskId);
    }
}
