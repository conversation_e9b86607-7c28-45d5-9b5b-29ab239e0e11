package com.taobao.wireless.orange.manager.task;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderOperationDO;
import com.taobao.wireless.orange.manager.release.OperationContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 任务操作处理器
 * 负责根据操作记录异步处理任务相关操作
 */
@Component
@Slf4j
public class TaskOperationProcessor {

    @Autowired
    private List<TaskOperationHandler> taskOperationHandlers;

    /**
     * 异步处理操作记录相关的任务操作
     *
     * @param operation 操作记录
     * @param context   操作上下文
     */
    @Async("taskExecutor")
    public void processOperationAsync(OReleaseOrderOperationDO operation, OperationContext context) {
        log.info("Processing operation async: operationId={}, type={}, releaseVersion={}", 
                operation.getId(), operation.getType(), operation.getReleaseVersion());

        try {
            // 遍历所有任务操作处理器
            for (TaskOperationHandler handler : taskOperationHandlers) {
                if (handler.supports(operation.getType())) {
                    handler.handle(operation, context);
                }
            }
        } catch (Exception e) {
            log.error("Error processing operation async: operationId={}, type={}", 
                    operation.getId(), operation.getType(), e);
        }
    }
}
