package com.taobao.wireless.orange.manager.task;

import com.taobao.wireless.orange.common.event.OperationRecordCreatedEvent;

/**
 * 任务创建策略接口
 */
public interface TaskCreationStrategy {

    /**
     * 判断是否需要创建任务
     *
     * @param event 操作记录创建事件
     * @return 是否需要创建任务
     */
    boolean shouldCreateTask(OperationRecordCreatedEvent event);

    /**
     * 创建任务
     *
     * @param event 操作记录创建事件
     * @return 创建的任务ID，如果没有创建任务则返回null
     */
    String createTask(OperationRecordCreatedEvent event);

    /**
     * 获取策略支持的操作类型
     *
     * @return 操作类型数组
     */
    String[] getSupportedOperationTypes();
}
