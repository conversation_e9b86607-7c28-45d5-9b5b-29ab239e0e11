package com.taobao.wireless.orange.manager.task;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderOperationDO;
import com.taobao.wireless.orange.manager.release.OperationContext;

/**
 * 任务操作处理器接口
 */
public interface TaskOperationHandler {

    /**
     * 判断是否支持处理该操作类型
     *
     * @param operationType 操作类型
     * @return 是否支持
     */
    boolean supports(OperationType operationType);

    /**
     * 处理操作记录相关的任务操作
     *
     * @param operation 操作记录
     * @param context   操作上下文
     */
    void handle(OReleaseOrderOperationDO operation, OperationContext context);
}
