package com.taobao.wireless.orange.manager;

import com.taobao.wireless.orange.common.constant.enums.TaskBizType;
import com.taobao.wireless.orange.common.constant.enums.TaskStatus;
import com.taobao.wireless.orange.dal.enhanced.dao.OTaskDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OTaskDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 任务管理器
 */
@Service
@Slf4j
public class TaskManager {

    @Autowired
    private OTaskDAO taskDAO;

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskStatus(String taskId, TaskStatus status) {
        OTaskDO task = new OTaskDO();
        task.setTaskId(taskId);
        task.setStatus(status);

        taskDAO.lambdaUpdate()
                .eq(OTaskDO::getTaskId, taskId)
                .set(OTaskDO::getStatus, status.getCode())
                .update();

        log.info("Updated task status: taskId={}, status={}", taskId, status.getCode());
    }

    /**
     * 根据业务ID和业务类型查询任务
     *
     * @param bizId   业务ID
     * @param bizType 业务类型
     * @return 任务
     */
    public OTaskDO getTaskByBizIdAndType(String bizId, TaskBizType bizType) {
        return taskDAO.lambdaQuery()
                .eq(OTaskDO::getBizId, bizId)
                .eq(OTaskDO::getBizType, bizType.getCode())
                .one();
    }

    /**
     * 检查是否存在未完成的任务
     *
     * @param bizId   业务ID
     * @param bizType 业务类型
     * @return 是否存在未完成任务
     */
    public boolean hasUnfinishedTask(String bizId, TaskBizType bizType) {
        return taskDAO.lambdaQuery()
                .eq(OTaskDO::getBizId, bizId)
                .eq(OTaskDO::getBizType, bizType.getCode())
                .notIn(OTaskDO::getStatus,
                       TaskStatus.COMPLETED.getCode(),
                       TaskStatus.CANCELED.getCode()
                )
                .exists();
    }
}
