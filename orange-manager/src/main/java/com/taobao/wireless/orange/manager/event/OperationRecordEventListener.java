package com.taobao.wireless.orange.manager.event;

import com.taobao.wireless.orange.common.event.OperationRecordCreatedEvent;
import com.taobao.wireless.orange.manager.task.TaskCreationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 操作记录事件监听器
 * 监听操作记录创建事件，并根据策略判断是否需要创建任务
 */
@Component
@Slf4j
public class OperationRecordEventListener {

    @Autowired
    private List<TaskCreationStrategy> taskCreationStrategies;

    /**
     * 监听操作记录创建事件
     *
     * @param event 操作记录创建事件
     */
    @EventListener
    @Async("eventTaskExecutor")
    public void handleOperationRecordCreated(OperationRecordCreatedEvent event) {
        log.info("Received operation record created event: operationId={}, releaseVersion={}, operationType={}", 
                event.getOperationId(), event.getReleaseVersion(), event.getOperationType());

        try {
            // 遍历所有任务创建策略
            for (TaskCreationStrategy strategy : taskCreationStrategies) {
                if (strategy.shouldCreateTask(event)) {
                    String taskId = strategy.createTask(event);
                    if (taskId != null) {
                        log.info("Task created by strategy {}: taskId={}, operationId={}", 
                                strategy.getClass().getSimpleName(), taskId, event.getOperationId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error handling operation record created event: operationId={}", 
                    event.getOperationId(), e);
        }
    }
}
