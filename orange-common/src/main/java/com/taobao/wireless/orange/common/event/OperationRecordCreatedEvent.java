package com.taobao.wireless.orange.common.event;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 操作记录创建事件
 */
@Getter
public class OperationRecordCreatedEvent extends ApplicationEvent {

    /**
     * 操作记录ID
     */
    private final Long operationId;

    /**
     * 发布版本号
     */
    private final String releaseVersion;

    /**
     * 操作类型
     */
    private final OperationType operationType;

    /**
     * 应用KEY
     */
    private final String appKey;

    /**
     * 命名空间ID
     */
    private final String namespaceId;

    /**
     * 操作参数
     */
    private final String params;

    public OperationRecordCreatedEvent(Object source, Long operationId, String releaseVersion, 
                                     OperationType operationType, String appKey, String namespaceId, String params) {
        super(source);
        this.operationId = operationId;
        this.releaseVersion = releaseVersion;
        this.operationType = operationType;
        this.appKey = appKey;
        this.namespaceId = namespaceId;
        this.params = params;
    }
}
