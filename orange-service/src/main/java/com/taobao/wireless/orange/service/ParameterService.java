package com.taobao.wireless.orange.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterVersionDO;
import com.taobao.wireless.orange.manager.ParameterManager;
import com.taobao.wireless.orange.manager.model.ParameterBO;
import com.taobao.wireless.orange.manager.util.PageUtil;
import com.taobao.wireless.orange.service.model.ParameterConditionDTO;
import com.taobao.wireless.orange.service.model.ParameterDetailDTO;
import com.taobao.wireless.orange.service.model.ParameterQueryDTO;
import com.taobao.wireless.orange.service.model.ReleaseOrderDTO;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;
import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_NAME;

@Service
public class ParameterService {
    @Autowired
    private ParameterManager parameterManager;

    @Autowired
    private OConditionDAO conditionDAO;

    @AttributeValidate
    public PaginationResult<ParameterDetailDTO> query(@NotNull(message = "参数查询条件不能为空") ParameterQueryDTO query, Pagination pagination) {
        // 转换查询条件
        ParameterBO parameterCondition = BeanUtil.createFromProperties(query, ParameterBO.class);

        // 查询参数数据
        Page<ParameterBO> pageResult = parameterManager.query(parameterCondition, pagination);

        PaginationResult<ParameterDetailDTO> result = PageUtil.convert(pageResult, ParameterDetailDTO.class);

        // 如果没有数据，直接返回空结果
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return result;
        }

        // 转换结果并设置
        result.setData(convertToDetailDTO(query.getNamespaceId(), pageResult.getRecords()));
        return result;
    }

    public List<ParameterDetailDTO> convertToDetailDTO(String namespaceId, List<ParameterBO> params) {
        // 获取条件ID到名称的映射
        Map<String, OConditionDO> conditionId2Condition = conditionDAO.getConditionMapByNamespaceId(namespaceId);
        // 转换结果并设置
        return params.stream()
                .map(param -> convertToDetailDTO(param, conditionId2Condition))
                .collect(Collectors.toList());
    }

    private ParameterDetailDTO convertToDetailDTO(ParameterBO parameterBO, Map<String, OConditionDO> conditionId2Condition) {
        return Pipe.of(parameterBO)
                // 基本属性复制
                .map(p -> BeanUtil.createFromProperties(p, ParameterDetailDTO.class))
                // 设置参数版本信息
                .apply(d -> setParameterVersionInfo(d, parameterBO.getParameterVersion()))
                // 设置参数条件信息
                .apply(d -> setParameterConditionInfo(d, parameterBO.getParameterConditionVersions(), conditionId2Condition))
                // 设置发布单
                .apply(d -> d.setInPublishReleaseOrder(BeanUtil.createFromProperties(parameterBO.getInPublishReleaseOrder(), ReleaseOrderDTO.class)))
                .get();
    }

    private void setParameterVersionInfo(ParameterDetailDTO detailDTO, OParameterVersionDO versionDO) {
        if (versionDO == null) {
            return;
        }

        detailDTO.setReleaseVersion(versionDO.getReleaseVersion());
        detailDTO.setPreviousReleaseVersion(versionDO.getPreviousReleaseVersion());
        detailDTO.setConditionsOrder(versionDO.getConditionsOrder());
    }

    private void setParameterConditionInfo(
            ParameterDetailDTO detailDTO,
            List<OParameterConditionVersionDO> conditionVersions,
            Map<String, OConditionDO> conditionId2Condition) {
        if (CollectionUtils.isEmpty(conditionVersions)) {
            return;
        }

        List<ParameterConditionDTO> conditionDTOList = conditionVersions.stream()
                .map(conditionVersion -> {
                    ParameterConditionDTO conditionDTO = BeanUtil.createFromProperties(
                            conditionVersion, ParameterConditionDTO.class);

                    // 设置条件信息
                    fillConditionDetail(conditionDTO, conditionVersion.getConditionId(), conditionId2Condition);

                    return conditionDTO;
                })
                .collect(Collectors.toList());

        detailDTO.setParameterConditions(conditionDTOList);
    }

    private void fillConditionDetail(
            ParameterConditionDTO conditionDTO,
            String conditionId,
            Map<String, OConditionDO> conditionId2Condition) {
        if (DEFAULT_CONDITION_ID.equals(conditionId)) {
            conditionDTO.setConditionName(DEFAULT_CONDITION_NAME);
        } else {
            Optional.ofNullable(conditionId2Condition.get(conditionId))
                    .ifPresent(c -> {
                        conditionDTO.setConditionName(c.getName());
                        conditionDTO.setConditionColor(c.getColor());
                    });
        }
    }

}
