package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.constant.enums.TaskBizType;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.manager.TaskManager;
import com.taobao.wireless.orange.service.model.TaskDTO;
import com.taobao.wireless.orange.service.model.TaskQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 任务服务
 */
@Service
@Slf4j
public class TaskService {

    @Autowired
    private TaskManager taskManager;

    /**
     * 查询任务列表
     *
     * @param query      查询条件
     * @param pagination 分页信息
     * @return 任务列表
     */
    @AttributeValidate
    public PaginationResult<TaskDTO> query(@NotNull(message = "任务查询条件不能为空") TaskQueryDTO query,
                                           @NotNull(message = "分页信息不能为空") Pagination pagination) {
        // TODO: 实现任务查询逻辑
        return new PaginationResult<>();
    }
}
