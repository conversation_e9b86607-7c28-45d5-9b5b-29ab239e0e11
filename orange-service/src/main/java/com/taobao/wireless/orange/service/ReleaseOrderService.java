package com.taobao.wireless.orange.service;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO;
import com.taobao.wireless.orange.manager.ReleaseOrderManager;
import com.taobao.wireless.orange.manager.model.*;
import com.taobao.wireless.orange.manager.util.PageUtil;
import com.taobao.wireless.orange.service.model.*;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;
import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_NAME;

@Service
public class ReleaseOrderService {
    @Autowired
    private ReleaseOrderManager releaseOrderManager;
    @Autowired
    private ParameterService parameterService;
    @Autowired
    private ConditionService conditionService;
    @Autowired
    private OConditionDAO conditionDAO;

    @AttributeValidate
    public PaginationResult<ReleaseOrderDTO> query(@NotNull(message = "发布单查询条件不能为空") ReleaseOrderQueryDTO query,
                                                   @NotNull(message = "分页信息不能为空") Pagination pagination) {
        return Pipe.of(query)
                .map(q -> BeanUtil.createFromProperties(query, ReleaseOrderBO.class))
                .map(q -> releaseOrderManager.query(q, pagination))
                .map(r -> PageUtil.convert(r, ReleaseOrderDTO.class))
                .get();
    }

    @AttributeValidate
    public Result<Map<ReleaseOrderStatus, Long>> countByStatus(String namespaceId) {
        return Pipe.of(namespaceId)
                .map(releaseOrderManager::countByStatus)
                .map(Result::new)
                .get();
    }

    @AttributeValidate
    public Result<String> create(@NotNull(message = "发布单创建条件不能为空") ReleaseOrderCreateDTO releaseOrder) {
        return Pipe.of(releaseOrder)
                .map(this::convert)
                .map(releaseOrderManager::create)
                .map(Result::new)
                .get();
    }

    @AttributeValidate
    public Result<Void> publish(String releaseVersion) {
        releaseOrderManager.publish(releaseVersion);
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> cancel(String releaseVersion) {
        releaseOrderManager.cancel(releaseVersion);
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> ratioGray(String releaseVersion, RatioGrayDTO ratioGray) {
        releaseOrderManager.ratioGray(releaseVersion, ratioGray.getPercent());
        return Result.success();
    }

    @AttributeValidate
    public Result<Void> verifyReply(String releaseVersion, @NotNull(message = "验证回复不能为空") VerifyReplyDTO verifyReplyDTO) {
        releaseOrderManager.verifyReply(releaseVersion,
                BeanUtil.createFromProperties(verifyReplyDTO, VerifyReplyBO.class));
        return Result.success();
    }

    public Result<ReleaseOrderDetailDTO> getDetail(String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getDetail)
                .map(this::convert)
                .map(Result::new)
                .get();
    }

    public Result<ReleaseOrderChangesDTO> getChanges(String releaseVersion) {
        return Pipe.of(releaseVersion)
                .map(releaseOrderManager::getChanges)
                .map(this::convert)
                .map(Result::new)
                .get();
    }

    public Result<List<ReleaseOrderOperationDTO>> getOperations(String releaseVersion, List<OperationType> operationTypes) {
        return Pipe.of(releaseOrderManager.getOperations(releaseVersion, operationTypes))
                .map(operations -> {
                    return BeanUtil.createFromProperties(operations, ReleaseOrderOperationDTO.class);
                })
                .map(Result::new)
                .get();
    }

    private ReleaseOrderChangesDTO convert(ReleaseOrderChangeBO parameterChanges) {
        ReleaseOrderChangesDTO releaseOrderChangesDTO = BeanUtil.createFromProperties(parameterChanges, ReleaseOrderChangesDTO.class);
        releaseOrderChangesDTO.setPreviousParametersDetail(parameterService.convertToDetailDTO(parameterChanges.getNamespaceId(), parameterChanges.getPreviousParameters()));
        releaseOrderChangesDTO.setPreviousConditionsDetail(conditionService.convertToDetailDTO(parameterChanges.getPreviousConditions()));
        releaseOrderChangesDTO.setParameterChanges(convertToParameterDetailDTO(parameterChanges.getParameterChanges()));
        releaseOrderChangesDTO.setConditionChanges(convertToConditionDetailDTO(parameterChanges.getConditionChanges()));
        return releaseOrderChangesDTO;
    }

    private List<ParameterDetailDTO> convertToParameterDetailDTO(List<ParameterVersionBO> parameterVersions) {
        List<String> conditionIds = parameterVersions.stream()
                .flatMap(p -> p.getParameterConditionVersions().stream())
                .map(ParameterConditionVersionBO::getConditionId)
                .distinct()
                .toList();
        Map<String, OConditionDO> conditionId2Condition = conditionDAO.getConditionMap(conditionIds);
        return parameterVersions.stream().map(p -> {
            ParameterDetailDTO parameterDetailDTO = BeanUtil.createFromProperties(p, ParameterDetailDTO.class);
            List<ParameterConditionDTO> parameterConditions = BeanUtil.createFromProperties(p.getParameterConditionVersions(), ParameterConditionDTO.class)
                    .stream()
                    .peek(c -> {
                        if (DEFAULT_CONDITION_ID.equals(c.getConditionId())) {
                            c.setConditionName(DEFAULT_CONDITION_NAME);
                        } else {
                            var condition = conditionId2Condition.get(c.getConditionId());
                            c.setConditionName(condition.getName());
                            c.setConditionColor(condition.getColor());
                        }
                    })
                    .toList();
            parameterDetailDTO.setParameterConditions(parameterConditions);
            return parameterDetailDTO;
        }).toList();
    }

    private List<ConditionDetailDTO> convertToConditionDetailDTO(List<ConditionVersionBO> conditionVersions) {
        return conditionVersions.stream().map(c -> {
            ConditionDetailDTO conditionDetailDTO = BeanUtil.createFromProperties(c, ConditionDetailDTO.class);
            conditionDetailDTO.setExpression(JSON.parse(c.getExpression(), ConditionExpressionDTO.class));
            return conditionDetailDTO;
        }).toList();
    }

    private ReleaseOrderBO convert(ReleaseOrderCreateDTO releaseOrder) {
        ReleaseOrderBO releaseOrderBO = BeanUtil.createFromProperties(releaseOrder, ReleaseOrderBO.class);

        List<ParameterChangeDTO> parameterChanges = releaseOrder.getParameterChanges();
        if (CollectionUtils.isNotEmpty(parameterChanges)) {
            List<ParameterVersionBO> parameterVersionBOS = parameterChanges.stream().map(parameterChangeDTO -> {
                ParameterVersionBO parameterVersionBO = BeanUtil.createFromProperties(parameterChangeDTO, ParameterVersionBO.class);
                parameterVersionBO.setParameterBO(BeanUtil.createFromProperties(parameterChangeDTO, ParameterBO.class));
                parameterVersionBO.setConditionNamesOrder(parameterChangeDTO.getConditionNamesOrder());

                List<ParameterConditionChangeDTO> parameterConditionChanges = parameterChangeDTO.getParameterConditionChanges();
                if (CollectionUtils.isNotEmpty(parameterConditionChanges)) {
                    List<ParameterConditionVersionBO> parameterConditionVersionBOS = BeanUtil.createFromProperties(parameterConditionChanges, ParameterConditionVersionBO.class);
                    parameterVersionBO.setParameterConditionVersions(parameterConditionVersionBOS);
                }

                return parameterVersionBO;
            }).collect(Collectors.toList());
            releaseOrderBO.setParameterVersions(parameterVersionBOS);
        }

        List<ConditionChangeDTO> conditionChanges = releaseOrder.getConditionChanges();
        List<ConditionVersionBO> conditionVersionBOS = CollectionUtils.isNotEmpty(conditionChanges) ? conditionChanges.stream().map(conditionChangeDTO -> {
            ConditionVersionBO conditionVersionBO = BeanUtil.createFromProperties(conditionChangeDTO, ConditionVersionBO.class);
            conditionVersionBO.setCondition(BeanUtil.createFromProperties(conditionChangeDTO, OConditionDO.class));
            conditionVersionBO.setExpression(JSON.toJSONString(conditionChangeDTO.getExpression()));
            return conditionVersionBO;
        }).collect(Collectors.toList()) : new ArrayList<>();
        releaseOrderBO.setConditionVersions(conditionVersionBOS);

        return releaseOrderBO;
    }

    private ReleaseOrderDetailDTO convert(ReleaseOrderBO releaseOrder) {
        ReleaseOrderDetailDTO releaseOrderDetailDTO = BeanUtil.createFromProperties(releaseOrder, ReleaseOrderDetailDTO.class);
        if (CollectionUtils.isNotEmpty(releaseOrder.getParameterVersions())) {
            releaseOrderDetailDTO.setParameterKeys(releaseOrder.getParameterVersions().stream().map(ParameterVersionBO::getParameterKey).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(releaseOrder.getConditionVersions())) {
            releaseOrderDetailDTO.setConditionIds(releaseOrder.getConditionVersions().stream().map(ConditionVersionBO::getConditionId).collect(Collectors.toList()));
        }
        return releaseOrderDetailDTO;
    }
}
