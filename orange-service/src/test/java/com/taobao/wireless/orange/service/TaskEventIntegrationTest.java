package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.TaskBizType;
import com.taobao.wireless.orange.common.constant.enums.TaskStatus;
import com.taobao.wireless.orange.common.event.OperationRecordCreatedEvent;
import com.taobao.wireless.orange.dal.enhanced.entity.OTaskDO;
import com.taobao.wireless.orange.manager.TaskManager;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 任务事件集成测试
 */
@SpringBootTest
@ActiveProfiles("testing")
public class TaskEventIntegrationTest {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private TaskManager taskManager;

    @Test
    public void testVerifyTaskCreationOnStartVerifyEvent() throws InterruptedException {
        // 准备测试数据
        String releaseVersion = "test-release-v1.0.0";
        String namespaceId = "test-namespace";
        String appKey = "test-app";

        // 发布START_VERIFY事件
        OperationRecordCreatedEvent event = new OperationRecordCreatedEvent(
                this,
                1L,
                releaseVersion,
                OperationType.START_VERIFY,
                appKey,
                namespaceId,
                null
        );

        eventPublisher.publishEvent(event);

        // 等待异步处理完成
        Thread.sleep(1000);

        // 验证任务是否创建
        OTaskDO task = taskManager.getTaskByBizIdAndType(releaseVersion, TaskBizType.RELEASE_ORDER);
        assertNotNull(task, "验证任务应该被创建");
        assertEquals(TaskStatus.PENDING.getCode(), task.getStatus(), "任务状态应该是PENDING");
        assertEquals(releaseVersion, task.getBizId(), "业务ID应该是发布版本号");
    }

    @Test
    public void testReviewTaskCreationOnApplyReleaseEvent() throws InterruptedException {
        // 准备测试数据
        String releaseVersion = "test-release-v2.0.0";
        String namespaceId = "test-namespace";
        String appKey = "test-app";

        // 发布APPLY_RELEASE事件
        OperationRecordCreatedEvent event = new OperationRecordCreatedEvent(
                this,
                2L,
                releaseVersion,
                OperationType.APPLY_RELEASE,
                appKey,
                namespaceId,
                null
        );

        eventPublisher.publishEvent(event);

        // 等待异步处理完成
        Thread.sleep(1000);

        // 验证任务是否创建
        OTaskDO task = taskManager.getTaskByBizIdAndType(releaseVersion, TaskBizType.RELEASE_ORDER);
        assertNotNull(task, "审核任务应该被创建");
        assertEquals(TaskStatus.PENDING.getCode(), task.getStatus(), "任务状态应该是PENDING");
        assertEquals(releaseVersion, task.getBizId(), "业务ID应该是发布版本号");
    }

    @Test
    public void testNoTaskCreationOnOtherEvents() throws InterruptedException {
        // 准备测试数据
        String releaseVersion = "test-release-v3.0.0";
        String namespaceId = "test-namespace";
        String appKey = "test-app";

        // 发布RELEASE事件（不应该创建任务）
        OperationRecordCreatedEvent event = new OperationRecordCreatedEvent(
                this,
                3L,
                releaseVersion,
                OperationType.RELEASE,
                appKey,
                namespaceId,
                null
        );

        eventPublisher.publishEvent(event);

        // 等待异步处理完成
        Thread.sleep(1000);

        // 验证任务不应该被创建
        OTaskDO task = taskManager.getTaskByBizIdAndType(releaseVersion, TaskBizType.RELEASE_ORDER);
        assertNull(task, "RELEASE操作不应该创建任务");
    }
}
