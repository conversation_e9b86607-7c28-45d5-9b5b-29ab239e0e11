package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.taobao.wireless.orange.common.constant.enums.ParameterStatus;
import com.taobao.wireless.orange.dal.enhanced.dao.OParameterDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OParameterMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 参数表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OParameterDAOImpl extends ServiceImpl<OParameterMapper, OParameterDO> implements OParameterDAO {
    @Override
    public boolean saveOrUpdateBatchByParameterId(List<OParameterDO> parameters) {
        return SqlHelper.saveOrUpdateBatch(this.getEntityClass(), this.getMapperClass(), this.log, parameters, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            LambdaQueryWrapper<OParameterDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OParameterDO::getParameterId, entity.getParameterId());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(1);
            param.put(Constants.WRAPPER, queryWrapper);
            return CollectionUtils.isEmpty(sqlSession.selectList(this.getSqlStatement(SqlMethod.SELECT_MAPS), param));
        }, (sqlSession, entity) -> {
            LambdaQueryWrapper<OParameterDO> lambdaUpdateWrapper = new LambdaQueryWrapper<>();
            lambdaUpdateWrapper.eq(OParameterDO::getParameterId, entity.getParameterId());
            Map<String, Object> param = CollectionUtils.newHashMapWithExpectedSize(2);
            param.put(Constants.ENTITY, entity);
            param.put(Constants.WRAPPER, lambdaUpdateWrapper);
            sqlSession.update(this.getSqlStatement(SqlMethod.UPDATE), param);
        });
    }

    @Override
    public List<OParameterDO> getParametersByParameterIds(List<String> parameterIds) {
        return this.lambdaQuery()
                .select(OParameterDO::getParameterId, OParameterDO::getParameterKey)
                .in(OParameterDO::getParameterId, parameterIds)
                .ne(OParameterDO::getStatus, ParameterStatus.INVALID)
                .list();
    }

    @Override
    public Map<String, OParameterDO> getParameterMapByParameterIds(List<String> parameterIds) {
        return this.getParametersByParameterIds(parameterIds)
                .stream()
                .collect(Collectors.toMap(OParameterDO::getParameterId, Function.identity(), (v1, v2) -> v1));
    }
}
