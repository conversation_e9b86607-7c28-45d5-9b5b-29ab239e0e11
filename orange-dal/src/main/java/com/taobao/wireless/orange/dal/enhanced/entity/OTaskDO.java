package com.taobao.wireless.orange.dal.enhanced.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.taobao.wireless.orange.common.constant.enums.TaskBizType;
import com.taobao.wireless.orange.common.constant.enums.TaskStatus;
import com.taobao.wireless.orange.common.constant.enums.TaskType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Getter
@Setter
@TableName("o_task")
public class OTaskDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务唯一ID
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 任务类型
     */
    @TableField("type")
    private TaskType type;

    /**
     * 任务状态
     */
    @TableField("status")
    private TaskStatus status;

    /**
     * 关联业务ID
     */
    @TableField("biz_id")
    private String bizId;

    /**
     * 关联业务类型
     */
    @TableField("biz_type")
    private TaskBizType bizType;

    /**
     * 任务描述
     */
    @TableField("description")
    private String description;

    /**
     * 任务创建时间
     */
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    private Date gmtCreate;

    /**
     * 最后更新时间
     */
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    private Date gmtModified;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 修改人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
}
