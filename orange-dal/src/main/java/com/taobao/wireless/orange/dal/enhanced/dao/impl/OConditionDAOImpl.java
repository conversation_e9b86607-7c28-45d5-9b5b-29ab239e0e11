package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.common.constant.enums.ConditionStatus;
import com.taobao.wireless.orange.dal.enhanced.dao.OConditionDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OConditionMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 条件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OConditionDAOImpl extends ServiceImpl<OConditionMapper, OConditionDO> implements OConditionDAO {

    @Override
    public List<OConditionDO> getByNames(String namespaceId, List<String> conditionNames) {
        return this.lambdaQuery()
                .eq(OConditionDO::getNamespaceId, namespaceId)
                .in(OConditionDO::getName, conditionNames)
                .ne(OConditionDO::getStatus, ConditionStatus.INVALID)
                .list();
    }

    @Override
    public OConditionDO getByConditionId(String conditionId) {
        return this.lambdaQuery()
                .eq(OConditionDO::getConditionId, conditionId)
                .ne(OConditionDO::getStatus, ConditionStatus.INVALID)
                .one();
    }

    @Override
    public Map<String, OConditionDO> getConditionMapByNamespaceId(String namespaceId) {
        return this.lambdaQuery()
                .eq(OConditionDO::getNamespaceId, namespaceId)
                .ne(OConditionDO::getStatus, ConditionStatus.INVALID)
                .list()
                .stream()
                .collect(Collectors.toMap(OConditionDO::getConditionId, Function.identity()));
    }

    @Override
    public List<String> getOrderedConditionIdsByName(String namespaceId, List<String> conditionNames) {
        if (CollectionUtils.isEmpty(conditionNames)) {
            return new ArrayList<>();
        }

        Map<String, String> conditionName2Id = this.getByNames(namespaceId, conditionNames)
                .stream()
                .collect(Collectors.toMap(OConditionDO::getName, OConditionDO::getConditionId));

        return conditionNames.stream()
                .map(conditionName2Id::get)
                .collect(Collectors.toList());
    }

    /**
     * 获取条件ID到名称的映射
     */
    @Override
    public Map<String, String> getConditionId2NameMap(List<String> conditionIds) {
        return this.lambdaQuery()
                .select(OConditionDO::getConditionId, OConditionDO::getName)
                .in(OConditionDO::getConditionId, conditionIds)
                .list()
                .stream()
                .collect(Collectors.toMap(OConditionDO::getConditionId, OConditionDO::getName));
    }

    @Override
    public Map<String, OConditionDO> getConditionMap(List<String> conditionIds) {
        return this.lambdaQuery()
                .in(OConditionDO::getConditionId, conditionIds)
                .list()
                .stream()
                .collect(Collectors.toMap(OConditionDO::getConditionId, Function.identity()));
    }
}
