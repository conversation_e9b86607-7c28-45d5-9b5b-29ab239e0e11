package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.taobao.wireless.orange.dal.enhanced.entity.OTaskHandlerDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OTaskHandlerMapper;
import com.taobao.wireless.orange.dal.enhanced.dao.OTaskHandlerDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 任务处理人 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
public class OTaskHandlerDAOImpl extends ServiceImpl<OTaskHandlerMapper, OTaskHandlerDO> implements OTaskHandlerDAO {

}
