package com.taobao.wireless.orange.dal.enhanced.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.wireless.orange.dal.enhanced.dao.OReleaseOrderDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.dal.enhanced.mapper.OReleaseOrderMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 发布单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class OReleaseOrderDAOImpl extends ServiceImpl<OReleaseOrderMapper, OReleaseOrderDO> implements OReleaseOrderDAO {

    @Override
    public OReleaseOrderDO getByReleaseVersion(String releaseVersion) {
        return lambdaQuery().eq(OReleaseOrderDO::getReleaseVersion, releaseVersion).one();
    }

    @Override
    public Map<String, OReleaseOrderDO> getReleaseOrderMapByReleaseVersions(List<String> releaseVersions) {
        return lambdaQuery().in(OReleaseOrderDO::getReleaseVersion, releaseVersions)
                .list()
                .stream()
                .collect(Collectors.toMap(OReleaseOrderDO::getReleaseVersion, Function.identity(), (v1, v2) -> v1));
    }
}
