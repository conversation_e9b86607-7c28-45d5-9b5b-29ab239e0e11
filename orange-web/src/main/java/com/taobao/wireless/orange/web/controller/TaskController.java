package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.constant.enums.TaskBizType;
import com.taobao.wireless.orange.common.constant.enums.TaskStatus;
import com.taobao.wireless.orange.common.constant.enums.TaskType;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.TaskService;
import com.taobao.wireless.orange.service.model.TaskDTO;
import com.taobao.wireless.orange.service.model.TaskQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 任务控制器
 */
@RestController
@RequestMapping("/api/task")
public class TaskController {

    @Autowired
    private TaskService taskService;

    /**
     * 查询任务列表
     */
    @PostMapping("/query")
    public PaginationResult<TaskDTO> query(@RequestBody TaskQueryDTO query, Pagination pagination) {
        return taskService.query(query, pagination);
    }

    /**
     * 根据任务ID获取任务详情
     */
    @GetMapping("/{taskId}")
    public Result<TaskDTO> getByTaskId(@PathVariable String taskId) {
        return taskService.getByTaskId(taskId);
    }

    /**
     * 更新任务状态
     */
    @PutMapping("/{taskId}/status")
    public Result<Void> updateStatus(@PathVariable String taskId, @RequestParam TaskStatus status) {
        return taskService.updateStatus(taskId, status);
    }

    /**
     * 创建任务
     */
    @PostMapping("/create")
    public Result<String> createTask(@RequestParam TaskType taskType,
                                    @RequestParam TaskBizType bizType,
                                    @RequestParam String bizId,
                                    @RequestParam String description) {
        return taskService.createTask(taskType, bizType, bizId, description);
    }

    /**
     * 检查是否存在未完成的任务
     */
    @GetMapping("/check-unfinished")
    public Result<Boolean> hasUnfinishedTask(@RequestParam String bizId, @RequestParam TaskBizType bizType) {
        return taskService.hasUnfinishedTask(bizId, bizType);
    }
}
