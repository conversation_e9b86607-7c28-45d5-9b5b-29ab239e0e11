package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.event.OperationRecordCreatedEvent;
import com.taobao.wireless.orange.common.model.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

/**
 * 事件测试控制器
 * 用于测试操作记录事件和任务创建功能
 */
@RestController
@RequestMapping("/api/test/event")
public class EventTestController {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 手动触发操作记录创建事件
     */
    @PostMapping("/trigger-operation-record")
    public Result<String> triggerOperationRecordEvent(@RequestParam String releaseVersion,
                                                      @RequestParam OperationType operationType,
                                                      @RequestParam String appKey,
                                                      @RequestParam String namespaceId,
                                                      @RequestParam(required = false) String params) {
        
        // 创建并发布事件
        OperationRecordCreatedEvent event = new OperationRecordCreatedEvent(
                this,
                System.currentTimeMillis(), // 使用时间戳作为操作ID
                releaseVersion,
                operationType,
                appKey,
                namespaceId,
                params
        );

        eventPublisher.publishEvent(event);

        return Result.success("Event published successfully");
    }

    /**
     * 触发验证任务创建事件
     */
    @PostMapping("/trigger-verify-task")
    public Result<String> triggerVerifyTaskEvent(@RequestParam String releaseVersion,
                                                @RequestParam String appKey,
                                                @RequestParam String namespaceId) {
        return triggerOperationRecordEvent(releaseVersion, OperationType.START_VERIFY, appKey, namespaceId, null);
    }

    /**
     * 触发审核任务创建事件
     */
    @PostMapping("/trigger-review-task")
    public Result<String> triggerReviewTaskEvent(@RequestParam String releaseVersion,
                                                @RequestParam String appKey,
                                                @RequestParam String namespaceId) {
        return triggerOperationRecordEvent(releaseVersion, OperationType.APPLY_RELEASE, appKey, namespaceId, null);
    }
}
