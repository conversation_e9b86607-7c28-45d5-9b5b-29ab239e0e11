package com.taobao.wireless.orange.web.controller;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.service.ReleaseOrderService;
import com.taobao.wireless.orange.service.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "发布单管理接口")
@RestController
@RequestMapping("/api/release-orders")
public class ReleaseOrderController {

    @Autowired
    private ReleaseOrderService releaseOrderService;

    @ApiOperation("查询发布列表")
    @GetMapping
    public PaginationResult<ReleaseOrderDTO> query(ReleaseOrderQueryDTO query,
                                                   @RequestParam(defaultValue = "1") Integer page,
                                                   @RequestParam(defaultValue = "10") Integer size) {
        Pagination pagination = Pagination.builder()
                .pageNum(page)
                .pageSize(size)
                .build();
        return releaseOrderService.query(query, pagination);
    }

    @ApiOperation("查询发布单状态统计")
    @GetMapping("/count-by-status")
    public Result<Map<ReleaseOrderStatus, Long>> countByStatus(@RequestParam("namespaceId") String namespaceId) {
        return releaseOrderService.countByStatus(namespaceId);
    }

    @ApiOperation("创建发布单")
    @PostMapping
    public Result<String> create(@RequestBody ReleaseOrderCreateDTO releaseOrder) {
        return releaseOrderService.create(releaseOrder);
    }

    @ApiOperation("发布发布单")
    @PutMapping("/{releaseVersion}/publish")
    public Result<Void> publish(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.publish(releaseVersion);
    }

    @ApiOperation("取消发布单")
    @PutMapping("/{releaseVersion}/cancel")
    public Result<Void> cancel(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.cancel(releaseVersion);
    }

    @ApiOperation("灰度发布单")
    @PutMapping("/{releaseVersion}/ratio-gray")
    public Result<Void> ratioGray(@PathVariable("releaseVersion") String releaseVersion, @RequestBody RatioGrayDTO ratioGray) {
        return releaseOrderService.ratioGray(releaseVersion, ratioGray);
    }

    @ApiOperation("发布单验证反馈")
    @PutMapping("/{releaseVersion}/verify")
    public Result<Void> verify(@PathVariable("releaseVersion") String releaseVersion, @RequestBody VerifyReplyDTO verifyReply) {
        return releaseOrderService.verifyReply(releaseVersion, verifyReply);
    }

    @ApiOperation("查询发布版本详情")
    @GetMapping("/{releaseVersion}")
    public Result<ReleaseOrderDetailDTO> getDetail(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.getDetail(releaseVersion);
    }

    @ApiOperation("查询发布变更内容")
    @GetMapping("/{releaseVersion}/changes")
    public Result<ReleaseOrderChangesDTO> getChanges(@PathVariable("releaseVersion") String releaseVersion) {
        return releaseOrderService.getChanges(releaseVersion);
    }

    @ApiOperation("查询发布单操作记录列表")
    @GetMapping("/{releaseVersion}/operations")
    public Result<List<ReleaseOrderOperationDTO>> getOperations(@PathVariable("releaseVersion") String releaseVersion, @RequestParam(required = false) List<OperationType> operationTypes) {
        return releaseOrderService.getOperations(releaseVersion, operationTypes);
    }
}
