# 任务事件系统设计文档

## 概述

本文档描述了通过事件监听的方式，实现当新增操作记录时，任务服务监听新增操作记录，并判断是否要新增任务的功能。

## 系统架构

### 核心组件

1. **事件定义**
   - `OperationRecordCreatedEvent`: 操作记录创建事件

2. **事件发布**
   - `AbstractOperationTemplate`: 在创建操作记录后发布事件

3. **事件监听**
   - `OperationRecordEventListener`: 监听操作记录创建事件

4. **任务创建策略**
   - `TaskCreationStrategy`: 任务创建策略接口
   - `VerifyTaskCreationStrategy`: 验证任务创建策略
   - `ReviewTaskCreationStrategy`: 审核任务创建策略

5. **任务管理**
   - `TaskManager`: 任务管理器
   - `TaskService`: 任务服务

## 工作流程

1. **操作记录创建**
   - 用户执行发布操作（如申请发布、发起验证等）
   - `AbstractOperationTemplate.createOperationRecord()` 创建操作记录
   - 发布 `OperationRecordCreatedEvent` 事件

2. **事件监听处理**
   - `OperationRecordEventListener` 异步监听事件
   - 遍历所有 `TaskCreationStrategy` 实现
   - 根据策略判断是否需要创建任务

3. **任务创建**
   - 策略判断需要创建任务时，调用 `TaskManager.createTask()`
   - 任务信息保存到数据库
   - 记录日志

## 支持的操作类型和任务类型映射

| 操作类型 | 任务类型 | 策略类 | 说明 |
|---------|---------|--------|------|
| APPLY_RELEASE | REVIEW | ReviewTaskCreationStrategy | 申请发布时创建审核任务 |
| START_VERIFY | VERIFY | VerifyTaskCreationStrategy | 发起验证时创建验证任务 |

## 数据模型

### 任务表 (o_task)
```sql
CREATE TABLE o_task (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(64) NOT NULL COMMENT '任务唯一ID',
    type VARCHAR(32) NOT NULL COMMENT '任务类型',
    status VARCHAR(32) NOT NULL COMMENT '任务状态',
    biz_id VARCHAR(64) NOT NULL COMMENT '关联业务ID',
    biz_type VARCHAR(32) NOT NULL COMMENT '关联业务类型',
    description TEXT COMMENT '任务描述',
    gmt_create DATETIME NOT NULL COMMENT '创建时间',
    gmt_modified DATETIME NOT NULL COMMENT '修改时间',
    creator VARCHAR(64) COMMENT '创建者',
    modifier VARCHAR(64) COMMENT '修改者'
);
```

### 枚举定义

#### TaskType (任务类型)
- REVIEW: 审核任务
- VERIFY: 验证任务
- RELEASE: 发布任务
- ROLLBACK: 回滚任务
- GRAY: 灰度任务
- MONITOR: 监控任务

#### TaskStatus (任务状态)
- PENDING: 待处理
- PROCESSING: 处理中
- COMPLETED: 已完成
- CANCELED: 已取消
- FAILED: 失败
- TIMEOUT: 超时

#### TaskBizType (任务业务类型)
- RELEASE_ORDER: 发布单
- NAMESPACE: 命名空间
- PARAMETER: 参数
- CONDITION: 条件
- EXPERIMENT: 实验

## 配置

### 异步事件处理配置
```java
@Configuration
@EnableAsync
public class AsyncEventConfig {
    @Bean("eventTaskExecutor")
    public Executor eventTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("event-task-");
        return executor;
    }
}
```

## 扩展指南

### 添加新的任务创建策略

1. 实现 `TaskCreationStrategy` 接口
2. 使用 `@Component` 注解标记为Spring组件
3. 实现以下方法：
   - `shouldCreateTask()`: 判断是否需要创建任务
   - `createTask()`: 创建任务
   - `getSupportedOperationTypes()`: 返回支持的操作类型

示例：
```java
@Component
public class CustomTaskCreationStrategy implements TaskCreationStrategy {
    
    @Override
    public boolean shouldCreateTask(OperationRecordCreatedEvent event) {
        return OperationType.CUSTOM_OPERATION.equals(event.getOperationType());
    }
    
    @Override
    public String createTask(OperationRecordCreatedEvent event) {
        // 实现任务创建逻辑
        return taskManager.createTask(TaskType.CUSTOM, TaskBizType.CUSTOM, 
                                    event.getReleaseVersion(), "自定义任务");
    }
    
    @Override
    public String[] getSupportedOperationTypes() {
        return new String[]{OperationType.CUSTOM_OPERATION.getCode()};
    }
}
```

## 测试

运行集成测试验证功能：
```bash
mvn test -Dtest=TaskEventIntegrationTest
```

## 监控和日志

- 事件发布和处理过程都有详细的日志记录
- 可以通过日志追踪任务创建的完整流程
- 异常情况会记录错误日志，不会影响主流程

## 注意事项

1. 事件处理是异步的，不会阻塞主业务流程
2. 任务创建失败不会影响操作记录的创建
3. 相同业务ID和业务类型的未完成任务不会重复创建
4. 事件处理使用独立的线程池，避免影响其他业务
